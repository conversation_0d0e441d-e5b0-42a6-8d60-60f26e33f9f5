import * as pdfjsLib from "pdfjs-dist";
import { SyncRedactor } from "redact-pii";
import { jsPDF } from "jspdf";
import { InterviewQuestion, CandidateInfo } from "./openai";

import * as PDFWorker from "pdfjs-dist/build/pdf.worker.mjs";

let workerBlobUrls: string[] = [];

const cleanupWorkerBlobUrls = () => {
  workerBlobUrls.forEach((url) => {
    try {
      URL.revokeObjectURL(url);
    } catch (e) {
      console.error("Error revoking blob URL:", e);
    }
  });
  workerBlobUrls = [];
};

if (typeof window !== "undefined") {
  window.addEventListener("unload", cleanupWorkerBlobUrls);
}

const setWorkerSrc = () => {
  try {
    const workerCodeString =
      typeof PDFWorker === "string"
        ? PDFWorker
        : `var pdfjsWorker = ${JSON.stringify(PDFWorker)};
         // Initialize the worker global
         self.pdfjsWorker = pdfjsWorker;
         // Execute all exported functions
         for (var key in pdfjsWorker) {
           if (typeof pdfjsWorker[key] === 'function') {
             pdfjsWorker[key]();
           }
         }`;

    const blob = new Blob([workerCodeString], {
      type: "application/javascript",
    });

    const blobUrl = URL.createObjectURL(blob);
    workerBlobUrls.push(blobUrl);
    // Set the worker source to the blob URL
    pdfjsLib.GlobalWorkerOptions.workerSrc = blobUrl;
    console.log("Using embedded PDF.js worker blob");
  } catch (error) {
    console.error("Error setting up embedded PDF worker:", error);
  }
};

setWorkerSrc();

export async function extractTextFromPDF(pdfFile: File): Promise<string> {
  try {
    const arrayBuffer = await pdfFile.arrayBuffer();

    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
    const pdf = await loadingTask.promise;

    const numPages = pdf.numPages;
    let fullText = "";

    for (let i = 1; i <= numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();

      const pageText = textContent.items
        .map((item) => ("str" in item ? item.str : ""))
        .join(" ");

      fullText += pageText + "\n\n";
    }
    return fullText;
  } catch (error) {
    console.error("Error extracting text from PDF:", error);
    // throw new Error("Failed to extract text from PDF");
    return "";
  }
}

export function removePersonalInfo(text: string): string {
  try {
    const redactor = new SyncRedactor();

    const redactedText = redactor.redact(text);

    return redactedText;
  } catch (error) {
    console.error("Error removing personal information:", error);
    return text;
  }
}

export async function processPDF(pdfFile: File): Promise<string> {
  try {
    const extractedText = await extractTextFromPDF(pdfFile);

    const redactedText = removePersonalInfo(extractedText);

    return redactedText;
  } catch (error) {
    console.error("Error processing PDF:", error);
    return "";
  }
}

export interface PrintData {
  interviewerName: string;
  candidateName: string;
  role: string;
  otherNotes?: string;
  selectionStatus: "Selected" | "Rejected" | "";
  finalFeedback?: string;
  questions?: InterviewQuestion[];
  candidateInfo?: CandidateInfo;
}

export const generatePDF = async (data: PrintData): Promise<void> => {
  try {
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    const margin = 20;
    const pageWidth = 210;
    const contentWidth = pageWidth - (margin * 2);
    let y = 30;

    doc.setFont("helvetica");

    // Simple page check
    const newPageIfNeeded = (space: number) => {
      if (y + space > 270) {
        doc.addPage();
        y = 30;
      }
    };

    // Title
    doc.setFontSize(20);
    doc.text(data.candidateName, pageWidth / 2, y, { align: "center" });
    y += 15;

    // Date
    doc.setFontSize(12);
    doc.text(new Date().toLocaleDateString(), pageWidth / 2, y, { align: "center" });
    y += 20;

    // Interview Details
    doc.setFontSize(16);
    doc.setFont("helvetica", "bold");
    doc.text("Interview Details", margin, y);
    y += 10;

    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.text(`Interviewer: ${data.interviewerName}`, margin, y);
    y += 7;
    doc.text(`Candidate: ${data.candidateName}`, margin, y);
    y += 7;
    doc.text(`Role: ${data.role}`, margin, y);
    y += 7;

    if (data.selectionStatus) {
      doc.text(`Status: ${data.selectionStatus}`, margin, y);
      y += 7;
    }
    y += 10;

    // Candidate Info
    if (data.candidateInfo) {
      newPageIfNeeded(50);
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text("Candidate Profile", margin, y);
      y += 10;

      doc.setFontSize(12);
      doc.setFont("helvetica", "normal");

      if (data.candidateInfo.summary) {
        doc.setFont("helvetica", "bold");
        doc.text("Summary:", margin, y);
        y += 7;
        doc.setFont("helvetica", "normal");
        const summaryLines = doc.splitTextToSize(data.candidateInfo.summary, contentWidth);
        doc.text(summaryLines, margin, y);
        y += summaryLines.length * 7 + 5;
      }

      if (data.candidateInfo.experience?.length) {
        newPageIfNeeded(30);
        doc.setFont("helvetica", "bold");
        doc.text("Experience:", margin, y);
        y += 7;
        doc.setFont("helvetica", "normal");
        data.candidateInfo.experience.forEach(exp => {
          const expLines = doc.splitTextToSize(exp, contentWidth);
          doc.text(expLines, margin, y);
          y += expLines.length * 7 + 3;
        });
        y += 5;
      }

      if (data.candidateInfo.skills?.length) {
        newPageIfNeeded(20);
        doc.setFont("helvetica", "bold");
        doc.text("Skills:", margin, y);
        y += 7;
        doc.setFont("helvetica", "normal");
        const skillsText = data.candidateInfo.skills.join(", ");
        const skillsLines = doc.splitTextToSize(skillsText, contentWidth);
        doc.text(skillsLines, margin, y);
        y += skillsLines.length * 7 + 10;
      }
    }

    // Questions
    if (data.questions?.length) {
      newPageIfNeeded(30);
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text("Interview Questions", margin, y);
      y += 15;

      doc.setFontSize(12);
      data.questions.forEach((q, index) => {
        newPageIfNeeded(40);

        // Question
        doc.setFont("helvetica", "normal");
        const questionLines = doc.splitTextToSize(q.question.trim(), contentWidth);
        doc.text(questionLines, margin, y);
        y += questionLines.length * 7 + 5;

        // Code
        if (q.code) {
          const codeLines = doc.splitTextToSize(q.code.trim(), contentWidth);
          doc.text(codeLines, margin, y);
          y += codeLines.length * 7 + 5;
        }

        // Feedback
        if (q.notes) {
          doc.setFont("helvetica", "bold");
          doc.text("Feedback:", margin, y);
          y += 7;
          doc.setFont("helvetica", "normal");
          const notesLines = doc.splitTextToSize(q.notes.trim(), contentWidth);
          doc.text(notesLines, margin, y);
          y += notesLines.length * 7 + 5;
        }

        // Space between questions
        if (index < data.questions.length - 1) {
          y += 10;
        }
      });
    }

    // Final Feedback
    if (data.finalFeedback) {
      newPageIfNeeded(30);
      y += 10;
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text("Final Feedback", margin, y);
      y += 10;

      doc.setFontSize(12);
      doc.setFont("helvetica", "normal");
      const feedbackLines = doc.splitTextToSize(data.finalFeedback, contentWidth);
      doc.text(feedbackLines, margin, y);
    }

    doc.save(`${data.candidateName || "Candidate"}_Interview_Summary.pdf`);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
};
